#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPIO查询工具

提供便捷的GPIO引脚和功能查询接口
"""

from gpio_database_manager import GPIODatabaseManager
import argparse
import sys
from typing import List, Dict, Any

class GPIOQueryTool:
    def __init__(self, db_path: str = "gpio_database_v2.db"):
        """初始化查询工具"""
        self.db_manager = GPIODatabaseManager(db_path)
        
    def connect(self):
        """连接数据库"""
        return self.db_manager.connect()
    
    def close(self):
        """关闭数据库连接"""
        self.db_manager.close()
    
    def query_pin_info(self, pin_name: str, detailed: bool = False):
        """
        查询引脚详细信息
        
        Args:
            pin_name: 引脚名称
            detailed: 是否显示详细信息
        """
        print(f"\n查询引脚: {pin_name}")
        print("=" * 50)
        
        # 获取引脚的所有相关信息
        all_info = self.db_manager.query_by_pin_name(pin_name)
        
        if not all_info:
            print(f"未找到引脚 '{pin_name}' 的信息")
            return
        
        # 显示引脚定义信息
        if 'GPIO Pin definitions' in all_info:
            pin_def = all_info['GPIO Pin definitions'][0]
            print(f"引脚定义:")
            print(f"  物理引脚: {pin_def.get('Pins', 'N/A')}")
            print(f"  引脚名称: {pin_def.get('Pin_Name', 'N/A')}")
            print(f"  用途: {pin_def.get('Usage', 'N/A')}")
            print(f"  PCB名称: {pin_def.get('PCB_NAME', 'N/A')}")
            if pin_def.get('Note'):
                print(f"  备注: {pin_def.get('Note')}")
            if pin_def.get('Alternate'):
                print(f"  备用功能: {pin_def.get('Alternate')}")
            if pin_def.get('Additional'):
                print(f"  附加信息: {pin_def.get('Additional')}")
        
        # 显示AF配置信息
        if 'GPIO_AF' in all_info:
            af_info = all_info['GPIO_AF'][0]
            print(f"\nAF配置:")
            
            af_functions = []
            for af_num in range(16):
                af_col = f'AF{af_num}'
                if af_col in af_info and af_info[af_col] and af_info[af_col].strip():
                    af_functions.append((af_col, af_info[af_col]))
            
            if af_functions:
                for af_name, function in af_functions:
                    print(f"  {af_name}: {function}")
            else:
                print("  无可用的AF功能")
        
        if detailed:
            print(f"\n详细信息:")
            for sheet_name, data in all_info.items():
                print(f"  {sheet_name}:")
                for record in data:
                    for key, value in record.items():
                        if key != 'id' and value:
                            print(f"    {key}: {value}")
    
    def find_function(self, function_name: str, exact_match: bool = False):
        """
        查找功能对应的引脚
        
        Args:
            function_name: 功能名称
            exact_match: 是否精确匹配
        """
        print(f"\n查找功能: {function_name} ({'精确匹配' if exact_match else '模糊匹配'})")
        print("=" * 50)
        
        if exact_match:
            # 精确匹配
            results = []
            af_info = self.db_manager.query_gpio_af()
            
            for record in af_info:
                for af_num in range(16):
                    af_col = f'AF{af_num}'
                    if af_col in record and record[af_col] == function_name:
                        results.append({
                            'Pin_Name': record['Pin_Name'],
                            'AF_Number': af_col,
                            'Function_Name': record[af_col]
                        })
        else:
            # 模糊匹配
            results = self.db_manager.find_pins_by_function(function_name)
        
        if results:
            print(f"找到 {len(results)} 个匹配结果:")
            
            # 按引脚名称排序
            results.sort(key=lambda x: x['Pin_Name'])
            
            for result in results:
                print(f"  {result['Pin_Name']} - {result['AF_Number']}: {result['Function_Name']}")
        else:
            print(f"未找到包含 '{function_name}' 的功能")
    
    def list_all_functions(self, af_number: str = None):
        """
        列出所有功能
        
        Args:
            af_number: AF编号过滤 (如 'AF0', 'AF1' 等)
        """
        if af_number:
            print(f"\n列出所有 {af_number} 功能:")
        else:
            print(f"\n列出所有功能:")
        print("=" * 50)
        
        af_info = self.db_manager.query_gpio_af()
        functions = set()
        
        for record in af_info:
            if af_number:
                # 只查看指定的AF
                if af_number in record and record[af_number] and record[af_number].strip():
                    functions.add(record[af_number])
            else:
                # 查看所有AF
                for af_num in range(16):
                    af_col = f'AF{af_num}'
                    if af_col in record and record[af_col] and record[af_col].strip():
                        functions.add(record[af_col])
        
        functions = sorted(list(functions))
        
        if functions:
            print(f"共找到 {len(functions)} 个不同的功能:")
            for i, func in enumerate(functions, 1):
                print(f"  {i:3d}. {func}")
        else:
            print("未找到任何功能")
    
    def list_pins_by_usage(self, usage_filter: str = None):
        """
        按用途列出引脚
        
        Args:
            usage_filter: 用途过滤器（模糊匹配）
        """
        if usage_filter:
            print(f"\n列出用途包含 '{usage_filter}' 的引脚:")
        else:
            print(f"\n列出所有引脚及其用途:")
        print("=" * 50)
        
        pin_defs = self.db_manager.get_pin_definitions()
        
        if usage_filter:
            filtered_pins = [pin for pin in pin_defs 
                           if pin.get('Usage') and usage_filter.lower() in pin.get('Usage', '').lower()]
        else:
            filtered_pins = pin_defs
        
        if filtered_pins:
            print(f"找到 {len(filtered_pins)} 个引脚:")
            for pin in filtered_pins:
                usage = pin.get('Usage', 'N/A')
                pcb_name = pin.get('PCB_NAME', '')
                if pcb_name:
                    print(f"  {pin.get('Pin_Name', 'N/A'):8} - {usage} (PCB: {pcb_name})")
                else:
                    print(f"  {pin.get('Pin_Name', 'N/A'):8} - {usage}")
        else:
            print("未找到匹配的引脚")
    
    def show_exti_info(self, exti_id: str = None):
        """
        显示EXTI信息
        
        Args:
            exti_id: EXTI ID过滤器
        """
        if exti_id:
            print(f"\nEXTI {exti_id} 信息:")
        else:
            print(f"\n所有EXTI信息:")
        print("=" * 50)
        
        exti_info = self.db_manager.get_exti_info(exti_id)
        
        if exti_info:
            for exti in exti_info:
                print(f"EXTI {exti.get('EXTI_ID', 'N/A')}:")
                print(f"  IRQ编号: {exti.get('IRQn', 'N/A')}")
                print(f"  模式: {exti.get('mode', 'N/A')}")
                print(f"  触发类型: {exti.get('trig_type', 'N/A')}")
                print(f"  是否启用: {exti.get('is_enabled', 'N/A')}")
                print(f"  抢占优先级: {exti.get('Preempt_Priority', 'N/A')}")
                print(f"  子优先级: {exti.get('Sub_Priority', 'N/A')}")
                print()
        else:
            print("未找到EXTI信息")
    
    def show_database_summary(self):
        """显示数据库摘要"""
        print("\n数据库摘要:")
        print("=" * 50)
        
        table_info = self.db_manager.get_table_info()
        
        for table_name, info in table_info.items():
            if table_name not in ['sqlite_sequence', 'column_relationships']:
                print(f"{table_name}:")
                print(f"  记录数: {info['row_count']}")
                print(f"  列数: {len(info['columns'])}")
                print()
        
        # 显示列关联关系
        relationships = self.db_manager.get_column_relationships_info()
        if relationships:
            print("列关联关系:")
            for col_name, sheets in relationships.items():
                print(f"  '{col_name}' 关联: {', '.join(sheets)}")

def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description='GPIO查询工具')
    parser.add_argument('--db', default='gpio_database_v2.db', help='数据库文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 查询引脚信息
    pin_parser = subparsers.add_parser('pin', help='查询引脚信息')
    pin_parser.add_argument('pin_name', help='引脚名称')
    pin_parser.add_argument('--detailed', action='store_true', help='显示详细信息')
    
    # 查找功能
    func_parser = subparsers.add_parser('function', help='查找功能')
    func_parser.add_argument('function_name', help='功能名称')
    func_parser.add_argument('--exact', action='store_true', help='精确匹配')
    
    # 列出所有功能
    list_func_parser = subparsers.add_parser('list-functions', help='列出所有功能')
    list_func_parser.add_argument('--af', help='AF编号过滤 (如 AF0, AF1)')
    
    # 按用途列出引脚
    usage_parser = subparsers.add_parser('usage', help='按用途列出引脚')
    usage_parser.add_argument('usage_filter', nargs='?', help='用途过滤器')
    
    # EXTI信息
    exti_parser = subparsers.add_parser('exti', help='显示EXTI信息')
    exti_parser.add_argument('exti_id', nargs='?', help='EXTI ID')
    
    # 数据库摘要
    subparsers.add_parser('summary', help='显示数据库摘要')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建查询工具
    query_tool = GPIOQueryTool(args.db)
    
    if not query_tool.connect():
        print("无法连接到数据库")
        return
    
    try:
        # 执行相应的命令
        if args.command == 'pin':
            query_tool.query_pin_info(args.pin_name, args.detailed)
        elif args.command == 'function':
            query_tool.find_function(args.function_name, args.exact)
        elif args.command == 'list-functions':
            query_tool.list_all_functions(args.af)
        elif args.command == 'usage':
            query_tool.list_pins_by_usage(args.usage_filter)
        elif args.command == 'exti':
            query_tool.show_exti_info(args.exti_id)
        elif args.command == 'summary':
            query_tool.show_database_summary()
    
    finally:
        query_tool.close()

if __name__ == "__main__":
    main()
