#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件读取和数据库生成工具

读取GD32H757ZxT_Datasheet_GPIO.xlsx文件的所有工作表，
分析数据结构，并生成关联数据库文件。
"""

import pandas as pd
import sqlite3
import os
import re
from typing import Dict, List, Any
import json

class ExcelToDatabaseConverter:
    def __init__(self, excel_path: str, db_path: str = "gpio_database.db"):
        """
        初始化转换器
        
        Args:
            excel_path: Excel文件路径
            db_path: 数据库文件路径
        """
        self.excel_path = excel_path
        self.db_path = db_path
        self.conn = None
        self.sheets_data = {}
        self.column_mapping = {}  # 存储同名列的关联关系

    def clean_cell_value(self, value):
        """
        清理单元格值中的特殊字符

        Args:
            value: 原始单元格值

        Returns:
            清理后的值
        """
        if pd.isna(value) or value is None:
            return None

        # 转换为字符串
        text = str(value)

        # 替换 _x000D_ 为换行符
        text = text.replace('_x000D_', '\n')

        # 替换其他可能的十六进制编码字符
        # _x000A_ = 换行符 (LF)
        text = text.replace('_x000A_', '\n')
        # _x0009_ = 制表符 (TAB)
        text = text.replace('_x0009_', '\t')

        # 使用正则表达式替换其他十六进制编码的特殊字符
        # 格式: _x[4位十六进制]_
        def replace_hex_chars(match):
            hex_code = match.group(1)
            try:
                # 将十六进制转换为对应的字符
                char_code = int(hex_code, 16)
                if char_code == 0x0D:  # 回车符
                    return '\n'
                elif char_code == 0x0A:  # 换行符
                    return '\n'
                elif char_code == 0x09:  # 制表符
                    return '\t'
                elif char_code < 32 or char_code == 127:  # 控制字符
                    return ''  # 移除控制字符
                else:
                    return chr(char_code)
            except ValueError:
                return match.group(0)  # 如果转换失败，保持原样

        text = re.sub(r'_x([0-9A-Fa-f]{4})_', replace_hex_chars, text)

        # 清理多余的空白字符
        text = re.sub(r'\s+', ' ', text).strip()

        # 如果清理后为空字符串，返回None
        if not text:
            return None

        return text

    def connect_database(self):
        """连接到SQLite数据库"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            print(f"成功连接到数据库: {self.db_path}")
            return True
        except Exception as e:
            print(f"连接数据库失败: {str(e)}")
            return False
    
    def read_excel_sheets(self):
        """读取Excel文件的所有工作表"""
        try:
            # 检查文件是否存在
            if not os.path.exists(self.excel_path):
                print(f"错误：文件 {self.excel_path} 不存在")
                return False
            
            # 读取所有工作表
            excel_file = pd.ExcelFile(self.excel_path)
            print(f"发现 {len(excel_file.sheet_names)} 个工作表:")
            
            for i, sheet_name in enumerate(excel_file.sheet_names, 1):
                print(f"  {i}. {sheet_name}")
                
                # 读取工作表数据
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                
                # 存储工作表数据和基本信息
                self.sheets_data[sheet_name] = {
                    'dataframe': df,
                    'shape': df.shape,
                    'columns': df.columns.tolist(),
                    'non_null_counts': df.count().to_dict()
                }
                
                print(f"     - 形状: {df.shape[0]} 行 x {df.shape[1]} 列")
                print(f"     - 列名: {df.columns.tolist()}")
                print()
            
            return True
            
        except Exception as e:
            print(f"读取Excel文件失败: {str(e)}")
            return False
    
    def analyze_column_relationships(self):
        """分析各工作表间的列名关联关系"""
        print("分析列名关联关系:")
        print("=" * 60)
        
        # 收集所有列名
        all_columns = {}
        for sheet_name, sheet_info in self.sheets_data.items():
            for col in sheet_info['columns']:
                if col not in all_columns:
                    all_columns[col] = []
                all_columns[col].append(sheet_name)
        
        # 找出在多个工作表中出现的列名
        common_columns = {col: sheets for col, sheets in all_columns.items() 
                         if len(sheets) > 1}
        
        if common_columns:
            print("发现以下同名列（可以关联）:")
            for col, sheets in common_columns.items():
                print(f"  '{col}' 出现在: {', '.join(sheets)}")
                self.column_mapping[col] = sheets
        else:
            print("未发现同名列")
        
        print(f"\n所有唯一列名 ({len(all_columns)} 个):")
        for col, sheets in all_columns.items():
            if len(sheets) == 1:
                print(f"  '{col}' (仅在 {sheets[0]} 中)")
        
        print()
        return common_columns
    
    def create_database_schema(self):
        """创建数据库表结构"""
        if not self.conn:
            print("错误：数据库未连接")
            return False
        
        cursor = self.conn.cursor()
        
        try:
            # 为每个工作表创建一个表
            for sheet_name, sheet_info in self.sheets_data.items():
                table_name = f"sheet_{sheet_name.replace(' ', '_').replace('-', '_')}"
                df = sheet_info['dataframe']
                
                # 构建CREATE TABLE语句
                columns_sql = []
                columns_sql.append("id INTEGER PRIMARY KEY AUTOINCREMENT")
                
                for col in df.columns:
                    # 清理列名，确保符合SQL标准
                    clean_col = col.replace(' ', '_').replace('-', '_').replace('(', '').replace(')', '')
                    
                    # 根据数据类型确定SQL类型
                    sample_value = df[col].dropna().iloc[0] if not df[col].dropna().empty else None
                    
                    if sample_value is None:
                        sql_type = "TEXT"
                    elif isinstance(sample_value, (int, float)):
                        sql_type = "REAL"
                    else:
                        sql_type = "TEXT"
                    
                    columns_sql.append(f"{clean_col} {sql_type}")
                
                create_sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns_sql)})"
                cursor.execute(create_sql)
                
                print(f"创建表: {table_name}")
            
            # 如果有同名列，创建关联表
            if self.column_mapping:
                self.create_relationship_tables(cursor)
            
            self.conn.commit()
            print("数据库表结构创建完成")
            return True
            
        except Exception as e:
            print(f"创建数据库表结构失败: {str(e)}")
            return False
    
    def create_relationship_tables(self, cursor):
        """创建关联表来存储同名列的关系"""
        # 创建列关系映射表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS column_relationships (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                column_name TEXT NOT NULL,
                sheet_name TEXT NOT NULL,
                table_name TEXT NOT NULL,
                UNIQUE(column_name, sheet_name)
            )
        ''')
        
        # 插入列关系数据
        for col_name, sheet_names in self.column_mapping.items():
            for sheet_name in sheet_names:
                table_name = f"sheet_{sheet_name.replace(' ', '_').replace('-', '_')}"
                cursor.execute('''
                    INSERT OR IGNORE INTO column_relationships 
                    (column_name, sheet_name, table_name) VALUES (?, ?, ?)
                ''', (col_name, sheet_name, table_name))
        
        print("创建列关系映射表")
    
    def insert_data(self):
        """将Excel数据插入数据库"""
        if not self.conn:
            print("错误：数据库未连接")
            return False
        
        cursor = self.conn.cursor()
        
        try:
            for sheet_name, sheet_info in self.sheets_data.items():
                table_name = f"sheet_{sheet_name.replace(' ', '_').replace('-', '_')}"
                df = sheet_info['dataframe']
                
                # 清理列名
                clean_columns = [col.replace(' ', '_').replace('-', '_').replace('(', '').replace(')', '') 
                               for col in df.columns]
                
                # 准备插入语句
                placeholders = ', '.join(['?' for _ in clean_columns])
                insert_sql = f"INSERT INTO {table_name} ({', '.join(clean_columns)}) VALUES ({placeholders})"
                
                # 插入数据
                for _, row in df.iterrows():
                    # 处理NaN值
                    values = []
                    for value in row:
                        if pd.isna(value):
                            values.append(None)
                        else:
                            values.append(str(value))
                    
                    cursor.execute(insert_sql, values)
                
                print(f"插入 {len(df)} 行数据到表 {table_name}")
            
            self.conn.commit()
            print("所有数据插入完成")
            return True
            
        except Exception as e:
            print(f"插入数据失败: {str(e)}")
            return False
    
    def generate_summary_report(self):
        """生成数据库摘要报告"""
        if not self.conn:
            return
        
        cursor = self.conn.cursor()
        
        print("\n" + "="*60)
        print("数据库摘要报告")
        print("="*60)
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"数据库文件: {self.db_path}")
        print(f"总表数: {len(tables)}")
        print()
        
        for table in tables:
            table_name = table[0]
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            
            print(f"表名: {table_name}")
            print(f"  记录数: {count}")
            print(f"  列数: {len(columns)}")
            print(f"  列名: {[col[1] for col in columns]}")
            print()
        
        # 如果有关联关系，显示关联信息
        if self.column_mapping:
            print("列关联关系:")
            for col_name, sheet_names in self.column_mapping.items():
                table_names = [f'sheet_{s.replace(" ", "_").replace("-", "_")}' for s in sheet_names]
                print(f"  '{col_name}' 关联表: {table_names}")
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            print("数据库连接已关闭")
    
    def convert(self):
        """执行完整的转换流程"""
        print("开始Excel到数据库转换流程")
        print("="*60)
        
        # 1. 读取Excel文件
        if not self.read_excel_sheets():
            return False
        
        # 2. 分析列关联关系
        self.analyze_column_relationships()
        
        # 3. 连接数据库
        if not self.connect_database():
            return False
        
        # 4. 创建数据库表结构
        if not self.create_database_schema():
            return False
        
        # 5. 插入数据
        if not self.insert_data():
            return False
        
        # 6. 生成摘要报告
        self.generate_summary_report()
        
        # 7. 关闭连接
        self.close_connection()
        
        print(f"\n转换完成！数据库文件已保存为: {self.db_path}")
        return True

def main():
    """主函数"""
    excel_path = "excel/GD32H757ZxT_Datasheet_GPIO.xlsx"
    db_path = "gpio_database.db"
    
    # 创建转换器实例
    converter = ExcelToDatabaseConverter(excel_path, db_path)
    
    # 执行转换
    success = converter.convert()
    
    if success:
        print("\n✅ 转换成功完成！")
    else:
        print("\n❌ 转换过程中出现错误")

if __name__ == "__main__":
    main()
