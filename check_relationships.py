#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查column_relationships表的冗余情况
"""

from gpio_database_manager import GPIODatabaseManager

def main():
    db = GPIODatabaseManager()
    if not db.connect():
        return
    
    try:
        result = db.execute_custom_query('SELECT column_name, sheet_name, table_name FROM column_relationships')
        print('当前column_relationships表内容:')
        print(f"{'列名':<15} {'工作表名':<25} {'数据库表名':<25}")
        print("-" * 65)
        
        redundant = True
        for row in result:
            sheet_name = row['sheet_name']
            table_name = row['table_name']
            
            # 检查是否完全相等或只是字符清理的差异
            cleaned_sheet = sheet_name.replace(' ', '_').replace('-', '_')
            
            print(f"{row['column_name']:<15} {sheet_name:<25} {table_name:<25}")
            
            if cleaned_sheet != table_name:
                redundant = False
                print(f"  -> 差异: '{cleaned_sheet}' vs '{table_name}'")
        
        print(f"\n分析结果:")
        if redundant:
            print("✅ sheet_name 和 table_name 存在冗余，可以简化")
            print("建议：只保留 table_name，通过清理函数可以从 sheet_name 推导出 table_name")
        else:
            print("❌ sheet_name 和 table_name 存在实质差异，需要保留两个字段")
            
    finally:
        db.close()

if __name__ == "__main__":
    main()
