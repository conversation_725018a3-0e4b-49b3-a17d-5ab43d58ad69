#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPIO数据库管理工具

提供对生成的GPIO数据库的查询和管理功能，
支持通过同名列进行关联查询。
"""

import sqlite3
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple

class GPIODatabaseManager:
    def __init__(self, db_path: str = "gpio_database_v2.db"):
        """
        初始化数据库管理器

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.conn = None
        self.column_relationships = {}
        self.table_name_cache = {}  # 缓存表名映射

    def get_table_name_by_sheet(self, sheet_name: str) -> Optional[str]:
        """
        根据工作表名获取对应的数据库表名

        Args:
            sheet_name: 工作表名称

        Returns:
            对应的数据库表名，如果未找到返回None
        """
        if not self.conn:
            return None

        # 先检查缓存
        if sheet_name in self.table_name_cache:
            return self.table_name_cache[sheet_name]

        cursor = self.conn.cursor()
        try:
            cursor.execute("SELECT DISTINCT table_name FROM column_relationships WHERE sheet_name = ?", (sheet_name,))
            result = cursor.fetchone()
            if result:
                table_name = result['table_name']
                self.table_name_cache[sheet_name] = table_name
                return table_name
        except Exception as e:
            print(f"获取表名失败: {str(e)}")

        return None
        
    def connect(self):
        """连接到数据库"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
            self._load_column_relationships()
            print(f"成功连接到数据库: {self.db_path}")
            return True
        except Exception as e:
            print(f"连接数据库失败: {str(e)}")
            return False
    
    def _load_column_relationships(self):
        """加载列关联关系"""
        if not self.conn:
            return
        
        cursor = self.conn.cursor()
        try:
            cursor.execute("SELECT column_name, sheet_name, table_name FROM column_relationships")
            relationships = cursor.fetchall()
            
            for row in relationships:
                col_name = row['column_name']
                if col_name not in self.column_relationships:
                    self.column_relationships[col_name] = []
                self.column_relationships[col_name].append({
                    'sheet_name': row['sheet_name'],
                    'table_name': row['table_name']
                })
        except Exception as e:
            print(f"加载列关联关系失败: {str(e)}")
    
    def get_table_info(self, table_name: str = None) -> Dict[str, Any]:
        """
        获取表信息
        
        Args:
            table_name: 表名，如果为None则返回所有表信息
            
        Returns:
            表信息字典
        """
        if not self.conn:
            print("错误：数据库未连接")
            return {}
        
        cursor = self.conn.cursor()
        
        try:
            if table_name:
                # 获取指定表信息
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                count = cursor.fetchone()['count']
                
                return {
                    table_name: {
                        'columns': [{'name': col['name'], 'type': col['type']} for col in columns],
                        'row_count': count
                    }
                }
            else:
                # 获取所有表信息
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
                tables = cursor.fetchall()
                
                result = {}
                for table in tables:
                    table_name = table['name']
                    result.update(self.get_table_info(table_name))
                
                return result
                
        except Exception as e:
            print(f"获取表信息失败: {str(e)}")
            return {}
    
    def query_by_pin_name(self, pin_name: str) -> Dict[str, List[Dict]]:
        """
        根据引脚名称查询所有相关信息
        
        Args:
            pin_name: 引脚名称
            
        Returns:
            包含所有相关表数据的字典
        """
        if not self.conn:
            print("错误：数据库未连接")
            return {}
        
        cursor = self.conn.cursor()
        results = {}
        
        # 查找包含Pin Name列的所有表
        pin_name_tables = self.column_relationships.get('Pin Name', [])
        
        for table_info in pin_name_tables:
            table_name = table_info['table_name']
            try:
                cursor.execute(f"SELECT * FROM {table_name} WHERE Pin_Name = ?", (pin_name,))
                rows = cursor.fetchall()
                
                if rows:
                    results[table_info['sheet_name']] = [dict(row) for row in rows]
                    
            except Exception as e:
                print(f"查询表 {table_name} 失败: {str(e)}")
        
        return results
    
    def query_gpio_af(self, pin_name: str = None, af_number: str = None) -> List[Dict]:
        """
        查询GPIO AF配置
        
        Args:
            pin_name: 引脚名称
            af_number: AF编号 (如 'AF0', 'AF1' 等)
            
        Returns:
            查询结果列表
        """
        if not self.conn:
            print("错误：数据库未连接")
            return []
        
        cursor = self.conn.cursor()

        # 获取GPIO_AF表的实际表名
        af_table_name = self.get_table_name_by_sheet('GPIO_AF')
        if not af_table_name:
            print("错误：未找到GPIO_AF表")
            return []

        try:
            if pin_name and af_number:
                # 查询特定引脚的特定AF功能
                cursor.execute(f"SELECT Pin_Name, {af_number} FROM {af_table_name} WHERE Pin_Name = ?", (pin_name,))
            elif pin_name:
                # 查询特定引脚的所有AF功能
                cursor.execute(f"SELECT * FROM {af_table_name} WHERE Pin_Name = ?", (pin_name,))
            elif af_number:
                # 查询特定AF的所有引脚
                cursor.execute(f"SELECT Pin_Name, {af_number} FROM {af_table_name} WHERE {af_number} IS NOT NULL AND {af_number} != ''")
            else:
                # 查询所有AF配置
                cursor.execute(f"SELECT * FROM {af_table_name}")
            
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
            
        except Exception as e:
            print(f"查询GPIO AF失败: {str(e)}")
            return []
    
    def find_pins_by_function(self, function_name: str) -> List[Dict]:
        """
        根据功能名称查找引脚
        
        Args:
            function_name: 功能名称（支持模糊匹配）
            
        Returns:
            匹配的引脚列表
        """
        if not self.conn:
            print("错误：数据库未连接")
            return []
        
        cursor = self.conn.cursor()
        results = []

        # 获取GPIO_AF表的实际表名
        af_table_name = self.get_table_name_by_sheet('GPIO_AF')
        if not af_table_name:
            print("错误：未找到GPIO_AF表")
            return []

        try:
            # 在GPIO_AF表中搜索
            af_columns = ['AF0', 'AF1', 'AF2', 'AF3', 'AF4', 'AF5', 'AF6', 'AF7',
                         'AF8', 'AF9', 'AF10', 'AF11', 'AF12', 'AF13', 'AF14', 'AF15']

            for af_col in af_columns:
                cursor.execute(f"""
                    SELECT Pin_Name, '{af_col}' as AF_Number, {af_col} as Function_Name
                    FROM {af_table_name}
                    WHERE {af_col} LIKE ? AND {af_col} IS NOT NULL AND {af_col} != ''
                """, (f'%{function_name}%',))
                
                rows = cursor.fetchall()
                results.extend([dict(row) for row in rows])
            
            return results
            
        except Exception as e:
            print(f"根据功能查找引脚失败: {str(e)}")
            return []
    
    def get_exti_info(self, exti_id: str = None) -> List[Dict]:
        """
        获取EXTI信息
        
        Args:
            exti_id: EXTI ID，如果为None则返回所有EXTI信息
            
        Returns:
            EXTI信息列表
        """
        if not self.conn:
            print("错误：数据库未连接")
            return []
        
        cursor = self.conn.cursor()

        # 获取EXTI表的实际表名
        exti_table_name = self.get_table_name_by_sheet('EXTI')
        if not exti_table_name:
            print("错误：未找到EXTI表")
            return []

        try:
            if exti_id:
                cursor.execute(f"SELECT * FROM {exti_table_name} WHERE EXTI_ID = ?", (exti_id,))
            else:
                cursor.execute(f"SELECT * FROM {exti_table_name}")
            
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
            
        except Exception as e:
            print(f"获取EXTI信息失败: {str(e)}")
            return []
    
    def get_pin_definitions(self, pin_name: str = None) -> List[Dict]:
        """
        获取引脚定义信息
        
        Args:
            pin_name: 引脚名称，如果为None则返回所有引脚定义
            
        Returns:
            引脚定义列表
        """
        if not self.conn:
            print("错误：数据库未连接")
            return []
        
        cursor = self.conn.cursor()

        # 获取GPIO Pin definitions表的实际表名
        pin_def_table_name = self.get_table_name_by_sheet('GPIO Pin definitions')
        if not pin_def_table_name:
            print("错误：未找到GPIO Pin definitions表")
            return []

        try:
            if pin_name:
                cursor.execute(f"SELECT * FROM {pin_def_table_name} WHERE Pin_Name = ?", (pin_name,))
            else:
                cursor.execute(f"SELECT * FROM {pin_def_table_name}")
            
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
            
        except Exception as e:
            print(f"获取引脚定义失败: {str(e)}")
            return []
    
    def execute_custom_query(self, sql: str, params: tuple = ()) -> List[Dict]:
        """
        执行自定义SQL查询
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
        """
        if not self.conn:
            print("错误：数据库未连接")
            return []
        
        cursor = self.conn.cursor()
        
        try:
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
            
        except Exception as e:
            print(f"执行自定义查询失败: {str(e)}")
            return []
    
    def get_column_relationships_info(self) -> Dict[str, List[str]]:
        """
        获取列关联关系信息
        
        Returns:
            列关联关系字典
        """
        result = {}
        for col_name, tables in self.column_relationships.items():
            result[col_name] = [table['sheet_name'] for table in tables]
        return result
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            print("数据库连接已关闭")

def main():
    """主函数 - 演示数据库管理器的使用"""
    # 创建数据库管理器
    db_manager = GPIODatabaseManager()
    
    # 连接数据库
    if not db_manager.connect():
        return
    
    print("\n" + "="*60)
    print("GPIO数据库管理器演示")
    print("="*60)
    
    # 1. 显示表信息
    print("\n1. 数据库表信息:")
    table_info = db_manager.get_table_info()
    for table_name, info in table_info.items():
        if table_name != 'sqlite_sequence':
            print(f"  {table_name}: {info['row_count']} 行, {len(info['columns'])} 列")
    
    # 2. 显示列关联关系
    print("\n2. 列关联关系:")
    relationships = db_manager.get_column_relationships_info()
    for col_name, sheets in relationships.items():
        print(f"  '{col_name}' 关联: {', '.join(sheets)}")
    
    # 3. 查询示例 - 根据引脚名称查询
    print("\n3. 查询引脚 'PA0' 的所有信息:")
    pa0_info = db_manager.query_by_pin_name('PA0')
    for sheet_name, data in pa0_info.items():
        print(f"  {sheet_name}: {len(data)} 条记录")
        if data:
            print(f"    示例: {list(data[0].keys())}")
    
    # 4. 查询GPIO AF配置
    print("\n4. 查询引脚 'PA0' 的AF配置:")
    af_info = db_manager.query_gpio_af('PA0')
    if af_info:
        print(f"  找到 {len(af_info)} 条AF配置")
        # 显示非空的AF功能
        for af_num in range(16):
            af_col = f'AF{af_num}'
            if af_col in af_info[0] and af_info[0][af_col]:
                print(f"    {af_col}: {af_info[0][af_col]}")
    
    # 5. 根据功能查找引脚
    print("\n5. 查找包含 'TIMER' 功能的引脚:")
    timer_pins = db_manager.find_pins_by_function('TIMER')
    if timer_pins:
        print(f"  找到 {len(timer_pins)} 个相关引脚")
        for i, pin_info in enumerate(timer_pins[:5]):  # 只显示前5个
            print(f"    {pin_info['Pin_Name']} - {pin_info['AF_Number']}: {pin_info['Function_Name']}")
        if len(timer_pins) > 5:
            print(f"    ... 还有 {len(timer_pins) - 5} 个")
    
    # 关闭连接
    db_manager.close()

if __name__ == "__main__":
    main()
