#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的数据库结构
"""

from gpio_database_manager import GPIODatabaseManager

def main():
    print("测试新的数据库结构")
    print("=" * 50)
    
    db = GPIODatabaseManager()
    if not db.connect():
        return
    
    try:
        # 1. 显示所有表名
        print("\n1. 数据库表名:")
        result = db.execute_custom_query('SELECT name FROM sqlite_master WHERE type="table"')
        for row in result:
            print(f"  {row['name']}")
        
        # 2. 显示column_relationships表内容
        print("\n2. column_relationships表内容:")
        relationships = db.execute_custom_query("SELECT * FROM column_relationships")
        for row in relationships:
            print(f"  {row['column_name']} -> {row['sheet_name']} -> {row['table_name']}")
        
        # 3. 测试查询功能
        print("\n3. 测试查询PA0引脚:")
        pa0_info = db.query_by_pin_name('PA0')
        for sheet_name, data in pa0_info.items():
            print(f"  {sheet_name}: {len(data)} 条记录")
        
        # 4. 测试AF查询
        print("\n4. 测试AF查询:")
        af_info = db.query_gpio_af('PA0')
        if af_info:
            print(f"  找到PA0的AF配置: {len(af_info)} 条")
        
        # 5. 测试功能查找
        print("\n5. 测试功能查找:")
        timer_pins = db.find_pins_by_function('TIMER1')
        print(f"  找到TIMER1相关引脚: {len(timer_pins)} 个")
        
        print("\n✅ 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    main()
