import pandas as pd

def check_data_format():
    """检查Excel文件中Functions description列的数据格式"""
    try:
        # 读取Excel文件
        df = pd.read_excel("excel/GD32H757ZxT_Datasheet_GPIO.xlsx")
        
        # 找到Functions description列
        func_desc_col = 'Functions description'
        
        print("前10行Functions description列的内容:")
        print("=" * 80)
        
        for i in range(min(10, len(df))):
            content = str(df.iloc[i][func_desc_col])
            print(f"行 {i+1}: {repr(content)}")
            print(f"内容: {content}")
            print("-" * 40)
        
        # 查看所有非空的Functions description内容
        non_empty = df[df[func_desc_col].notna() & (df[func_desc_col] != '')]
        print(f"\n总共有 {len(non_empty)} 行包含Functions description内容")
        
        if len(non_empty) > 0:
            print("\n所有非空Functions description内容的示例:")
            for i, (idx, row) in enumerate(non_empty.head(5).iterrows()):
                content = str(row[func_desc_col])
                print(f"示例 {i+1} (行{idx+1}): {content}")
                
    except Exception as e:
        print(f"检查过程中出现错误: {str(e)}")

if __name__ == "__main__":
    check_data_format()
