import pandas as pd

def verify_original_column():
    """验证原始Functions description列是否保持不变"""
    try:
        # 读取原始文件和处理后的文件
        original_df = pd.read_excel("excel/GD32H757ZxT_Datasheet_GPIO.xlsx")
        processed_df = pd.read_excel("excel/GD32H757ZxT_Datasheet_GPIO_Split.xlsx")
        
        print("验证原始Functions description列是否保持不变:")
        print("=" * 80)
        
        # 检查列是否存在
        if 'Functions description' not in processed_df.columns:
            print("❌ 错误：处理后的文件中没有找到Functions description列")
            return
        
        # 比较前5行的Functions description内容
        print("前5行Functions description列对比:")
        for i in range(min(5, len(original_df))):
            original_content = str(original_df.iloc[i]['Functions description'])
            processed_content = str(processed_df.iloc[i]['Functions description'])
            
            print(f"\n行 {i+1}:")
            print(f"Pin Name: {original_df.iloc[i]['Pin Name']}")
            print(f"原始内容: {repr(original_content)}")
            print(f"处理后内容: {repr(processed_content)}")
            print(f"是否相同: {'✅' if original_content == processed_content else '❌'}")
            print("-" * 40)
        
        # 检查所有行是否相同
        all_same = True
        different_rows = []
        
        for i in range(len(original_df)):
            original_content = str(original_df.iloc[i]['Functions description'])
            processed_content = str(processed_df.iloc[i]['Functions description'])
            
            if original_content != processed_content:
                all_same = False
                different_rows.append(i + 1)
        
        print(f"\n总体检查结果:")
        print(f"总行数: {len(original_df)}")
        if all_same:
            print("✅ 所有行的Functions description列内容完全相同")
        else:
            print(f"❌ 有 {len(different_rows)} 行内容不同")
            print(f"不同的行号: {different_rows[:10]}{'...' if len(different_rows) > 10 else ''}")
        
        # 显示新增的列
        print(f"\n新增的列:")
        new_columns = [col for col in processed_df.columns if col not in original_df.columns]
        for col in new_columns:
            print(f"  - {col}")
        
        # 显示拆分结果示例
        print(f"\n拆分结果示例 (第1行):")
        if len(processed_df) > 0:
            print(f"Pin Name: {processed_df.iloc[0]['Pin Name']}")
            print(f"原始Functions description: {processed_df.iloc[0]['Functions description']}")
            print(f"拆分后 - Default: {processed_df.iloc[0]['Default']}")
            print(f"拆分后 - Alternate: {processed_df.iloc[0]['Alternate']}")
            print(f"拆分后 - Additional: {processed_df.iloc[0]['Additional']}")
        
    except Exception as e:
        print(f"验证过程中出现错误: {str(e)}")

if __name__ == "__main__":
    verify_original_column()
