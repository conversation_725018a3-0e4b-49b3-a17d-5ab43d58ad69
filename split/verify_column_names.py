import pandas as pd
from openpyxl import load_workbook

def verify_column_names():
    """验证列名修改是否成功"""
    try:
        # 读取处理后的文件
        df = pd.read_excel("excel/GD32H757ZxT_Datasheet_GPIO_Split_New.xlsx")
        
        print("验证列名修改结果:")
        print("=" * 70)
        
        # 显示所有列名
        print("所有列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2}. {col}")
        
        # 检查关键列名是否正确
        print(f"\n关键列名检查:")
        print("-" * 40)
        
        expected_columns = {
            'original pin name': '原Pin Name列重命名',
            'Pin Name': '新Pin Name列（原Default）',
            'Alternate': 'Alternate列',
            'Additional': 'Additional列',
            'Functions description': '原Functions description列'
        }
        
        for col_name, description in expected_columns.items():
            if col_name in df.columns:
                print(f"✅ {col_name:20} - {description}")
            else:
                print(f"❌ {col_name:20} - {description} (未找到)")
        
        # 显示前几行数据来验证内容
        print(f"\n前3行数据验证:")
        print("-" * 40)
        
        key_columns = ['original pin name', 'Pin Name', 'Alternate', 'Additional']
        available_columns = [col for col in key_columns if col in df.columns]
        
        for i in range(min(3, len(df))):
            print(f"\n行 {i+1}:")
            for col in available_columns:
                value = str(df.iloc[i][col])
                if len(value) > 40:
                    value = value[:40] + "..."
                print(f"  {col:18}: {value}")
        
        # 验证数据内容是否正确
        print(f"\n数据内容验证:")
        print("-" * 40)
        
        if 'original pin name' in df.columns and 'Pin Name' in df.columns:
            # 检查original pin name列是否包含完整的引脚名称
            original_sample = str(df.iloc[0]['original pin name'])
            pin_name_sample = str(df.iloc[0]['Pin Name'])
            
            print(f"第1行数据示例:")
            print(f"  original pin name: {original_sample}")
            print(f"  Pin Name (新):     {pin_name_sample}")
            
            # 验证Pin Name列是否只包含引脚名称（如PE2, PE3等）
            pin_name_pattern_check = len(pin_name_sample) <= 10  # 引脚名称通常很短
            print(f"  Pin Name格式检查:  {'✅ 格式正确' if pin_name_pattern_check else '❌ 格式可能有问题'}")
        
        # 检查Functions description列是否保持不变
        print(f"\nFunctions description列检查:")
        print("-" * 40)
        
        if 'Functions description' in df.columns:
            func_desc_sample = str(df.iloc[0]['Functions description'])
            has_newlines = '\n' in func_desc_sample
            has_default_text = 'Default:' in func_desc_sample
            
            print(f"包含换行符: {'✅' if has_newlines else '❌'}")
            print(f"包含'Default:': {'✅' if has_default_text else '❌'}")
            print(f"内容示例: {func_desc_sample[:60]}...")
        
        # 使用openpyxl检查格式
        print(f"\n格式检查:")
        print("-" * 40)
        
        wb = load_workbook("excel/GD32H757ZxT_Datasheet_GPIO_Split_New.xlsx")
        ws = wb.active
        
        # 检查列宽
        column_widths = {
            'A': ('original pin name', 20),
            'I': ('Pin Name', 15),
            'J': ('Alternate', 60),
            'K': ('Additional', 25)
        }
        
        for col_letter, (col_desc, expected_width) in column_widths.items():
            actual_width = ws.column_dimensions[col_letter].width
            status = "✅" if actual_width == expected_width else "❌"
            print(f"{col_desc:18} (列{col_letter}): 宽度 {actual_width} {status}")
        
        print(f"\n文件信息:")
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print(f"文件路径: excel/GD32H757ZxT_Datasheet_GPIO_Split_New.xlsx")
        
    except Exception as e:
        print(f"验证过程中出现错误: {str(e)}")

if __name__ == "__main__":
    verify_column_names()
